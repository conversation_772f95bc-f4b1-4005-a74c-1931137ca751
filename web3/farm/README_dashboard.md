# Web3 Farm Dashboard 仪表板说明文档

## 概述

这是一个基于 Obsidian DataviewJS 的 Web3 农场投资项目仪表板，支持风险等级维度和日期维度的数据展示，提供项目投资分析、收益计算、APR 统计等功能。

## 文件结构

```
web3/farm/
├── dashboard.md          # 主仪表板文件
├── data/
│   ├── risk_percentage.md    # 风险等级配置
│   ├── invest_level_limit.md # 投资等级配置
│   └── token_prices.md       # 代币价格数据
└── history/                  # 项目历史数据
    ├── doing/               # 进行中项目
    └── done/                # 已完成项目
```

## 核心功能

### 1. 双维度视图
- **风险等级维度**：按风险等级分组显示项目，计算风险配置比例
- **日期维度**：按时间粒度（日/周/月/年）展示项目表现

### 2. 数据计算
- **APR 计算**：基于 USDT 价值的年化收益率
- **收益统计**：总收益、日收益、投资天数
- **仓位分析**：投入金额、仓位状态、操作建议

### 3. 智能处理
- **价格转换**：自动将各种代币转换为 USDT 价值
- **时间范围过滤**：日期维度严格按时间范围计算
- **数据验证**：完善的错误处理和数据校验

### 4. 视觉优化
- **颜色编码**：正负数自动颜色显示（绿色=正数，红色=负数）
- **风险等级**：不同风险等级使用不同颜色标识
- **APR Gap**：根据差距大小显示不同颜色警示
- **状态指示**：仓位状态使用图标和颜色组合显示

## 代码架构

### 模块化设计

代码采用模块化设计，分为以下几个核心模块：

1. **初始化模块**：全局变量和配置
2. **数据加载模块**：解析配置文件和项目数据
3. **时间聚合模块**：按不同粒度聚合日期数据
4. **基础计算模块**：核心计算逻辑
5. **衍生计算模块**：基于基础数据的衍生指标
6. **业务逻辑模块**：项目数据处理
7. **表格构建模块**：风险维度表格数据构建
8. **日期表格模块**：日期维度表格数据构建
9. **UI 组件模块**：用户界面和交互

### 关键函数说明

#### 数据加载
- `parseRiskFile()`: 解析风险配置文件
- `parseLevelFile()`: 解析投资等级配置
- `parsePriceData()`: 解析价格数据
- `loadAllData()`: 加载所有基础数据的统一入口

#### 核心计算
- `calculateProjectHistory()`: 计算项目完整历史数据
- `calculateProjectHistoryInTimeRange()`: 计算时间范围内的项目数据
- `calculateAPR()`: 计算年化收益率
- `calculateUSDTAmount()`: 计算 USDT 价值
- `calculateInvestDays()`: 计算投资天数

#### 衍生计算
- `calculateUSDTBasedAPR()`: 基于USDT价值计算APR
- `calculatePositionStatus()`: 计算仓位状态和建议操作
- `calculateAPRGap()`: 计算APR差距
- `calculatePortfolioAPR()`: 计算组合APR
- `calculateEarnings()`: 计算总收益和日收益

#### 时间处理
- `extractProjectDateData()`: 提取项目日期数据
- `aggregateByMonth/Week/Year()`: 按不同粒度聚合数据
- `aggregateByDay()`: 按日聚合数据（在buildDateTable中实现）

#### 表格构建
- `calculateRiskSummary()`: 计算风险等级汇总数据
- `buildRiskSummaryRow()`: 构建风险等级汇总行
- `buildProjectRow()`: 构建项目详情行
- `buildDateTable()`: 构建日期维度表格数据
- `buildTimeSummaryRow()`: 构建时间汇总行
- `buildDateProjectRow()`: 构建日期项目详情行

#### UI组件和渲染
- `renderFixedTable()`: 渲染带固定表头和第一列的表格
- `processCellContent()`: 处理单元格内容，清理Markdown格式
- `renderViewSwitcher()`: 渲染维度切换器UI
- `switchMainView()`: 切换主维度视图
- `switchDateView()`: 切换日期粒度视图
- `renderCurrentView()`: 渲染当前视图

#### 颜色格式化工具
- `formatColoredNumber()`: 正负数颜色格式化（数字）
- `formatColoredPercentage()`: 正负数颜色格式化（百分比）
- `getRiskColoredText()`: 风险等级颜色显示
- `getAPRGapColoredText()`: APR Gap颜色显示

## 数据格式规范

### 项目文件格式

```markdown
---
Chain: BSC
Fee: 0.5
Level: 1
Protocol: BitEqual
Risk: 4
Status: Doing
Type: Mint
Unit: USDC
Wallet: bn.web3
---
- [date:: 20250608 22:00:00] [balance:: 248.6] [remark:: ""]
- [date:: 20250630 10:00:00] [balance:: 250] [add:: 10] [remark:: ""]
```

### 配置文件格式

**风险配置 (risk_percentage.md)**:
```markdown
- [risk:: 0] [cn:: 无风险] [percent:: 0.3] [expect_apr:: 0.05]
- [risk:: 1] [cn:: 低风险] [percent:: 0.4] [expect_apr:: 0.15]
```

**价格数据 (token_prices.md)**:
```markdown
- [token:: BTC] [date:: 2025-06-01] [price:: 70000]
- [token:: ETH] [date:: 2025-06-01] [price:: 3500]
```

## 计算逻辑详解

### APR 计算公式

```javascript
// 基础 APR 计算
totalEarned = lastBalance - firstBalance
returnRate = totalEarned / firstBalance
APR = (returnRate / daysDiff) * 365 * 100

// USDT 基础 APR（考虑价格变化）
usdtEarned = usdtWithdrawAmount - usdtInvestAmount
usdtReturnRate = usdtEarned / usdtInvestAmount
APR = (usdtReturnRate / daysDiff) * 365 * 100
```

### 时间范围处理逻辑

#### 风险维度视图
- 使用项目的**完整历史数据**
- 计算从第一条记录到最后一条记录的总体表现

#### 日期维度视图
- **严格按时间范围过滤**：只使用指定时间范围内的记录
- **无数据则不显示**：如果时间范围内没有记录，该项目不出现在视图中
- **真实数据计算**：所有指标基于时间范围内的真实数据

### 追加投资处理

项目支持追加投资（add 属性），计算逻辑：
```javascript
adjustedFirstBalance = firstBalance + totalAddAmount
totalEarned = lastBalance - adjustedFirstBalance
```

## 重要注意事项

### 1. 时间范围计算的关键原则

**❌ 错误做法**：
- 强行计算跨时间范围的数据
- 使用线性插值估算缺失数据
- 将全历史数据强制分配到特定时间段

**✅ 正确做法**：
- 严格按时间范围过滤数据
- 无数据时显示 0 或不显示
- 保持数据的真实性和准确性

### 2. 数据一致性

- **风险视图**：只统计 `Status: Doing` 的项目
- **日期视图**：统计所有项目（包括 Done 状态）
- **价格数据**：稳定币（USDT/USDC/DAI）价格固定为 1

### 3. 错误处理

代码包含完善的错误处理机制：
- 日期解析错误处理
- 价格数据缺失处理
- 数据格式验证
- 计算异常捕获

## 性能优化

### 1. 数据缓存
- 全局变量缓存配置数据
- 避免重复解析相同文件

### 2. 计算优化
- 按需计算，避免不必要的数据处理
- 使用高效的数组操作和过滤

### 3. 渲染优化
- 固定表头和第一列
- 分页显示大量数据
- 懒加载机制

## 常见问题排查

### 1. 数据不显示
- 检查项目文件的 `Status` 字段
- 确认日期格式是否正确
- 验证 balance 字段是否存在

### 2. APR 计算异常
- 检查价格数据是否完整
- 确认投资天数计算是否正确
- 验证 add 属性的处理

### 3. 时间范围问题
- 确认时间粒度设置
- 检查日期解析逻辑
- 验证时间范围过滤条件

## 开发者指引

### 🎯 核心功能修改指南

#### 1. 调整表格列顺序

**影响范围**: 风险维度和日期维度的表格显示

**修改位置**:
```javascript
// 风险维度表格列顺序 (第957-1011行, 第1015-1043行)
window.DashboardTableBuilder.buildRiskSummaryRow()
window.DashboardTableBuilder.buildProjectRow()

// 日期维度表格列顺序 (第1299-1363行, 第1367-1396行)
window.DashboardDateTableBuilder.buildTimeSummaryRow()
window.DashboardDateTableBuilder.buildDateProjectRow()
```

**具体操作**:
1. **修改表头定义**: 在主执行逻辑中找到 `headers` 数组定义
2. **调整数据行构建**: 修改对应的 `buildXXXRow` 函数中的数组元素顺序
3. **保持一致性**: 确保表头和数据行的列数和顺序完全一致

**注意事项**:
- 必须同时修改风险维度和日期维度的对应函数
- 固定表头的第一列（风险等级/时间）不建议调整
- 修改后需要验证表格渲染是否正常

#### 2. 调整APR计算逻辑

**影响范围**: 所有APR相关的显示和计算

**核心函数位置**:
```javascript
// 基础APR计算 (第484-508行)
window.DashboardBaseCalculator.calculateAPR()

// USDT基础APR计算 (第706-726行)
window.DashboardDerivedCalculator.calculateUSDTBasedAPR()

// APR Gap计算 (第773-777行)
window.DashboardDerivedCalculator.calculateAPRGap()
```

**修改示例**:
```javascript
// 原始计算逻辑
APR = ((returnRate / daysDiff) * 365) * 100

// 修改为复利计算
APR = (Math.pow(1 + returnRate, 365/daysDiff) - 1) * 100

// 修改为考虑手续费
const feeRate = project.Fee || 0;
const netReturnRate = returnRate - feeRate;
APR = ((netReturnRate / daysDiff) * 365) * 100
```

**影响分析**:
- ✅ **局部影响**: 只影响APR显示，不影响其他计算
- ⚠️ **数据一致性**: 需要确保风险维度和日期维度使用相同逻辑
- 🔄 **历史数据**: 修改后所有历史项目的APR都会重新计算

#### 3. 调整字体颜色和样式

**影响范围**: 表格显示的视觉效果

**颜色配置位置**:
```javascript
// 风险等级颜色 (第154-167行)
window.DashboardUtils.getRiskColoredText()

// APR Gap颜色 (第170-183行)
window.DashboardUtils.getAPRGapColoredText()

// 表格渲染样式 (第75-111行)
window.DashboardTableRenderer.renderFixedTable()
```

**修改示例**:
```javascript
// 修改风险等级颜色
if (risk <= 1) {
    color = "#28a745"; // 原绿色
    color = "#00C851"; // 修改为新绿色
} else if (risk === 2) {
    color = "#ffc107"; // 原黄色
    color = "#ffbb33"; // 修改为新黄色
}

// 修改APR Gap阈值和颜色
if (gap >= 10) {
    return `<span style="color: #28a745; font-weight: bold;">${formattedValue}</span>`;
} else if (gap >= -5) { // 修改阈值从-10到-5
    return formattedValue;
}
```

**样式修改位置**:
```javascript
// 表格整体样式
tableHTML += `<th style="background-color: var(--background-primary);
    border: 1px solid var(--background-modifier-border);
    padding: 8px 12px;
    font-weight: bold;
    font-size: 14px; // 新增字体大小
    color: #333; // 新增字体颜色
    white-space: nowrap; ${stickyStyle}">${header}</th>`;
```

#### 4. 修改数据过滤和显示逻辑

**影响范围**: 哪些项目会显示在表格中

**关键过滤位置**:
```javascript
// 风险维度项目过滤 (第1410行)
const doingProjects = dv.pages(window.DashboardConfig.HISTORY_PATH).where(p => p.Status === "Doing");

// 日期维度项目过滤 (第1409行)
const allProjects = dv.pages(window.DashboardConfig.HISTORY_PATH);

// 时间范围过滤 (第559-569行)
const entriesInRange = balanceEntries.filter(entry => {
    return entryDateStr >= startDate && entryDateStr <= endDate;
});
```

**修改示例**:
```javascript
// 添加新的过滤条件
const doingProjects = dv.pages(window.DashboardConfig.HISTORY_PATH)
    .where(p => p.Status === "Doing" && p.Risk <= 3); // 只显示中风险及以下

// 修改时间范围逻辑
const entriesInRange = balanceEntries.filter(entry => {
    const entryDateStr = entryDate.toFormat("yyyy-MM-dd");
    // 添加缓冲期：前后各延长3天
    const bufferStart = dv.date(startDate).minus({days: 3}).toFormat("yyyy-MM-dd");
    const bufferEnd = dv.date(endDate).plus({days: 3}).toFormat("yyyy-MM-dd");
    return entryDateStr >= bufferStart && entryDateStr <= bufferEnd;
});
```

### 🔧 模块修改影响分析

#### 数据加载模块 (第189-275行)
**修改影响**: 🔴 **高风险** - 影响整个系统的数据源

**关键函数**:
- `parseRiskFile()`: 修改会影响风险等级显示和计算
- `parseLevelFile()`: 修改会影响仓位状态计算
- `parsePriceData()`: 修改会影响所有USDT转换

**修改建议**:
- 添加数据验证和错误处理
- 保持向后兼容性
- 修改后全面测试所有功能

#### 基础计算模块 (第417-696行)
**修改影响**: 🔴 **高风险** - 影响所有核心计算

**关键函数**:
- `calculateProjectHistory()`: 风险维度的核心计算基础
- `calculateProjectHistoryInTimeRange()`: 日期维度的核心计算基础
- `calculateAPR()`: APR计算的基础逻辑
- `calculateUSDTAmount()`: 价格转换的核心逻辑

**修改建议**:
- 修改前备份原始逻辑
- 添加详细的测试用例
- 确保新旧逻辑的数据一致性

#### 表格构建模块 (第895-1046行)
**修改影响**: 🟡 **中等风险** - 主要影响显示效果

**关键函数**:
- `calculateRiskSummary()`: 风险等级汇总计算
- `buildRiskSummaryRow()`: 风险等级行构建
- `buildProjectRow()`: 项目详情行构建

**修改建议**:
- 修改显示逻辑相对安全
- 注意保持数据计算的准确性
- 测试各种边界情况

#### UI组件模块 (第1524-1854行)
**修改影响**: 🟢 **低风险** - 主要影响用户交互

**关键功能**:
- 维度切换器
- 粒度选择器
- 表格渲染器

**修改建议**:
- 可以安全地修改样式和交互
- 注意保持状态管理的一致性
- 测试各种用户操作场景

### 🎨 常见定制需求

#### 1. 添加新的计算列

**步骤**:
1. **在衍生计算模块添加计算函数**:
```javascript
// 在 DashboardDerivedCalculator 中添加
calculateNewMetric: (projectData) => {
    // 新指标计算逻辑
    return calculatedValue;
}
```

2. **在业务逻辑模块调用**:
```javascript
// 在 processProject 函数中添加
const newMetric = window.DashboardDerivedCalculator.calculateNewMetric(projectData);
return {
    // 其他字段...
    newMetric: newMetric
};
```

3. **在表格构建中添加列**:
```javascript
// 在 buildProjectRow 函数中添加
return [
    // 其他列...
    project.newMetric || "0" // 新列
];
```

4. **更新表头定义**:
```javascript
// 在主执行逻辑中更新 headers 数组
const headers = [
    // 其他表头...
    "新指标" // 新表头
];
```

#### 2. 修改排序逻辑

**位置**: 主执行逻辑模块 (第1291-1296行)

**当前排序**:
```javascript
projectsInThisRisk.sort((a, b) => {
    const nameA = a.projectLink && a.projectLink.path ? a.projectLink.path.split('/').pop() : '';
    const nameB = b.projectLink && b.projectLink.path ? b.projectLink.path.split('/').pop() : '';
    return nameA.localeCompare(nameB);
});
```

**修改示例**:
```javascript
// 按APR降序排列
projectsInThisRisk.sort((a, b) => b.projectApr - a.projectApr);

// 按投入金额降序排列
projectsInThisRisk.sort((a, b) => b.amountInUsdt - a.amountInUsdt);

// 多字段排序
projectsInThisRisk.sort((a, b) => {
    // 首先按APR降序
    if (b.projectApr !== a.projectApr) {
        return b.projectApr - a.projectApr;
    }
    // 然后按投入金额降序
    return b.amountInUsdt - a.amountInUsdt;
});
```

#### 3. 添加数据导出功能

**实现位置**: UI组件模块

**添加导出按钮**:
```javascript
// 在维度切换器附近添加
const exportButton = `
    <button onclick="exportTableData()" style="
        margin-left: 10px;
        padding: 5px 10px;
        background: #007acc;
        color: white;
        border: none;
        border-radius: 3px;
        cursor: pointer;">
        导出数据
    </button>
`;
```

**添加导出函数**:
```javascript
window.exportTableData = () => {
    const currentView = window.DashboardViewState.mainView;
    const tableData = currentView === 'risk' ? riskTableData : dateTableData;

    // 转换为CSV格式
    const csvContent = tableData.map(row =>
        row.map(cell => `"${cell}"`).join(',')
    ).join('\n');

    // 下载文件
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `farm_dashboard_${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
};
```

### 🚨 高风险修改警告

#### ⚠️ 绝对不要修改的部分

1. **时间解析逻辑** (第124-139行):
```javascript
window.DashboardUtils.parseDate()
```
- 修改会导致所有日期相关功能失效
- 影响时间范围过滤和APR计算

2. **数据文件路径配置** (第20-25行):
```javascript
window.DashboardConfig
```
- 修改路径会导致数据加载失败
- 必须确保新路径下有对应的数据文件

3. **核心数据结构**:
- 项目文件的YAML头部字段名
- balance记录的属性名 (date, balance, add等)
- 配置文件的字段结构

#### ⚠️ 需要谨慎修改的部分

1. **全局变量名称**:
- 修改 `window.DashboardXXX` 的命名会影响模块间调用
- 需要全局搜索替换所有引用

2. **计算精度设置**:
```javascript
Math.round(daysDiff * 100) / 100  // 保留2位小数
```
- 修改精度会影响数据一致性
- 可能导致累计误差

3. **错误处理逻辑**:
- 移除 try-catch 可能导致系统崩溃
- 修改错误返回值会影响上层逻辑

### 🔍 调试和测试建议

#### 修改前的准备工作

1. **备份原始文件**:
```bash
cp dashboard_new.md dashboard_new_backup_$(date +%Y%m%d).md
```

2. **准备测试数据**:
- 创建包含各种边界情况的测试项目
- 准备不同时间范围的测试数据
- 确保有完整的价格数据

3. **制定测试计划**:
- 列出需要验证的功能点
- 准备预期结果对比
- 设计回归测试用例

#### 修改后的验证步骤

1. **基础功能验证**:
- 检查表格是否正常显示
- 验证数据计算是否正确
- 测试维度切换功能

2. **边界情况测试**:
- 空数据情况
- 单条记录项目
- 跨年度数据
- 异常日期格式

3. **性能测试**:
- 大量项目的加载速度
- 频繁切换的响应时间
- 内存使用情况

#### 常见问题的快速定位

1. **表格不显示**:
```javascript
// 在浏览器控制台检查
console.log('项目数据:', doingProjects);
console.log('表格数据:', combinedTableData);
console.log('错误信息:', error);
```

2. **计算结果异常**:
```javascript
// 检查中间计算步骤
console.log('历史数据:', historyData);
console.log('USDT金额:', usdtAmounts);
console.log('投资天数:', investDays);
```

3. **样式显示问题**:
```javascript
// 检查HTML生成
console.log('表格HTML:', tableHTML);
console.log('样式应用:', getComputedStyle(element));
```

这些指引将帮助开发者安全、高效地修改和扩展仪表板功能，避免常见的陷阱和问题。

### 📋 配置管理指南

#### 全局配置修改

**配置文件位置** (第20-25行):
```javascript
window.DashboardConfig = {
    HISTORY_PATH: '"web3/farm/history"',           // 项目数据路径
    RISK_LIMIT_PATH: 'web3/farm/data/risk_percentage',    // 风险配置
    PRICES_FILE_PATH: 'web3/farm/data/token_prices',      // 价格数据
    LEVEL_LIMIT_PATH: 'web3/farm/data/invest_level_limit' // 等级配置
};
```

**路径修改影响**:
- 修改 `HISTORY_PATH` 会改变项目数据来源
- 修改配置文件路径需要确保新位置有对应文件
- 路径格式必须符合 Dataview 的查询语法

#### 显示配置定制

**分页设置**:
```javascript
// 在主执行逻辑中查找分页相关代码
const ITEMS_PER_PAGE = 10; // 修改每页显示数量
const MAX_PAGES = 5;       // 修改最大页数显示
```

**表格样式配置**:
```javascript
// 在 renderFixedTable 函数中修改
const tableStyle = {
    maxHeight: '80vh',        // 表格最大高度
    fontSize: '14px',         // 字体大小
    borderRadius: '6px',      // 圆角大小
    borderColor: 'var(--background-modifier-border)' // 边框颜色
};
```

**颜色主题配置**:
```javascript
// 创建颜色配置对象
window.DashboardColors = {
    risk: {
        low: '#28a745',      // 低风险颜色
        medium: '#ffc107',   // 中风险颜色
        high: '#dc3545'      // 高风险颜色
    },
    apr: {
        positive: '#28a745', // 正收益颜色
        neutral: '#6c757d',  // 中性颜色
        negative: '#dc3545'  // 负收益颜色
    }
};
```

### 🔧 高级定制功能

#### 1. 自定义计算公式

**创建自定义APR计算**:
```javascript
// 在 DashboardDerivedCalculator 中添加
calculateCustomAPR: (historyData, customParams) => {
    const { riskAdjustment, feeRate, compoundFrequency } = customParams;

    // 基础收益率
    const baseReturn = (historyData.lastBalance - historyData.firstBalance) / historyData.firstBalance;

    // 风险调整
    const riskAdjustedReturn = baseReturn * (1 - riskAdjustment);

    // 手续费调整
    const netReturn = riskAdjustedReturn - feeRate;

    // 复利计算
    const periods = historyData.investDays / (365 / compoundFrequency);
    const apr = (Math.pow(1 + netReturn / periods, periods) - 1) * 100;

    return Math.round(apr * 100) / 100;
}
```

**应用自定义计算**:
```javascript
// 在 processProject 函数中
const customParams = {
    riskAdjustment: project.Risk * 0.01, // 风险等级调整
    feeRate: project.Fee || 0,           // 手续费率
    compoundFrequency: 12                // 月复利
};

const customApr = window.DashboardDerivedCalculator.calculateCustomAPR(historyData, customParams);
```

#### 2. 动态列配置

**创建列配置系统**:
```javascript
// 定义列配置
window.DashboardColumnConfig = {
    risk: [
        { key: 'riskLevel', title: '风险等级', width: '120px', sticky: true },
        { key: 'amount', title: '投入金额', width: '100px', format: 'currency' },
        { key: 'percentage', title: '占比', width: '80px', format: 'percent' },
        { key: 'status', title: '健康程度', width: '120px' },
        { key: 'project', title: '项目', width: '150px', type: 'link' },
        { key: 'position', title: '仓位状态', width: '120px' },
        { key: 'action', title: '建议操作', width: '100px' },
        { key: 'totalEarned', title: '总收益', width: '100px', format: 'currency' },
        { key: 'investDays', title: '投资天数', width: '80px', format: 'number' },
        { key: 'dailyEarned', title: '日收益', width: '100px', format: 'currency' },
        { key: 'currentApr', title: '当前APR', width: '80px', format: 'percent' },
        { key: 'expectApr', title: '期望APR', width: '80px', format: 'percent' },
        { key: 'aprGap', title: 'APR Gap', width: '80px', format: 'percent', color: 'aprGap' },
        { key: 'portfolioCurrentApr', title: '组合当前APR', width: '100px', format: 'percent' },
        { key: 'portfolioExpectApr', title: '组合期望APR', width: '100px', format: 'percent' },
        { key: 'portfolioAprGap', title: '组合APR Gap', width: '100px', format: 'percent', color: 'aprGap' }
    ],
    date: [
        // 日期维度的列配置...
    ]
};
```

**动态生成表头**:
```javascript
// 根据配置生成表头
const generateHeaders = (viewType) => {
    return window.DashboardColumnConfig[viewType].map(col => col.title);
};

// 根据配置生成数据行
const generateDataRow = (data, viewType) => {
    return window.DashboardColumnConfig[viewType].map(col => {
        let value = data[col.key];

        // 根据格式化类型处理数据
        switch (col.format) {
            case 'currency':
                value = window.DashboardUtils.formatNumber(value);
                break;
            case 'percent':
                value = window.DashboardUtils.formatPercentage(value);
                break;
            case 'number':
                value = value.toFixed(2);
                break;
        }

        // 根据颜色配置应用样式
        if (col.color === 'aprGap') {
            value = window.DashboardUtils.getAPRGapColoredText(value);
        }

        return value;
    });
};
```

## 技术实现细节

### 1. 时间范围过滤核心逻辑

```javascript
// 关键函数：calculateProjectHistoryInTimeRange
const entriesInRange = balanceEntries.filter(entry => {
    const entryDateStr = entryDate.toFormat("yyyy-MM-dd");
    return entryDateStr >= startDate && entryDateStr <= endDate;
});

// 如果时间范围内没有记录，直接返回null
if (entriesInRange.length === 0) return null;
```

**设计原则**：
- 严格按时间范围过滤，不做任何推测或插值
- 无数据时返回 null，上层逻辑会跳过该项目
- 保证数据的真实性和可靠性

### 2. 双计算路径设计

```javascript
// 风险维度：使用完整历史数据
const historyData = window.DashboardBaseCalculator.calculateProjectHistory(project, priceMap);

// 日期维度：使用时间范围限制数据
const historyData = window.DashboardBaseCalculator.calculateProjectHistoryInTimeRange(
    project, priceMap, timeRange.startDate, timeRange.endDate
);
```

这种设计确保两个维度的计算逻辑完全独立，互不影响。

### 3. 价格转换机制

```javascript
// 稳定币直接使用余额值
if (unit === 'USDT' || unit === 'USDC' || unit === 'DAI') {
    return {
        investAmount: firstBalance + totalAddAmount,
        withdrawAmount: lastBalance,
        priceFound: true
    };
}

// 其他代币需要价格转换
const prices = priceMap.get(unit);
totalInvestAmount += firstBalance * firstPrice;
```

### 4. 追加投资处理逻辑

```javascript
// 计算历史上所有add属性的总和
let totalAddAmount = 0;
for (const entry of balanceEntries) {
    if (entry.add !== undefined) {
        totalAddAmount += parseFloat(entry.add);
    }
}

// 调整初始余额
const adjustedFirstBalance = firstBalance + totalAddAmount;
```

## 数据流程图

```text
项目文件 → 数据解析 → 时间过滤 → 基础计算 → 衍生指标 → 表格渲染
    ↓         ↓         ↓         ↓         ↓         ↓
配置文件 → 风险/等级 → 日期聚合 → APR计算 → 仓位分析 → UI展示
    ↓         ↓         ↓         ↓         ↓         ↓
价格数据 → 价格映射 → 范围限制 → USDT转换 → 收益统计 → 交互控制
```

## 调试指南

### 1. 开启调试模式

在浏览器开发者工具中查看 console 输出：
```javascript
console.log('项目数据:', projectData);
console.log('时间范围:', timeRange);
console.log('计算结果:', calculationResult);
```

### 2. 常见调试场景

**场景1：项目不显示**
```javascript
// 检查数据加载
console.log('项目总数:', allProjects.length);
console.log('过滤后项目:', filteredProjects.length);

// 检查时间过滤
console.log('时间范围:', startDate, endDate);
console.log('项目日期:', projectDates);
```

**场景2：APR计算异常**
```javascript
// 检查历史数据
console.log('历史数据:', historyData);
console.log('投资天数:', investDays);
console.log('USDT金额:', usdtAmounts);
```

**场景3：价格转换问题**
```javascript
// 检查价格数据
console.log('价格映射:', priceMap);
console.log('代币单位:', unit);
console.log('价格查找结果:', prices.get(date));
```

## 最佳实践

### 1. 数据文件管理
- 保持项目文件命名规范：`YYYYMMDD-Protocol.Type.Unit.md`
- 定期清理无效的历史记录
- 确保价格数据的及时更新

### 2. 性能优化建议
- 避免在短时间内频繁切换维度
- 大量项目时考虑分页显示
- 定期清理浏览器缓存

### 3. 数据质量保证
- 新增项目时检查必要字段
- 定期验证价格数据的准确性
- 监控计算结果的合理性

## 故障排除手册

### 问题1：表格不显示或显示空白
**可能原因**：
- 数据文件路径错误
- 项目文件格式不正确
- JavaScript 执行错误

**解决方案**：
1. 检查 `DashboardConfig` 中的路径配置
2. 验证项目文件的 YAML 头部格式
3. 查看浏览器控制台的错误信息

### 问题2：APR 显示为 0 或异常值
**可能原因**：
- 价格数据缺失
- 投资天数计算错误
- balance 字段格式问题

**解决方案**：
1. 检查 `token_prices.md` 中对应代币的价格
2. 验证项目文件中的日期格式
3. 确认 balance 字段为数字格式

### 问题3：时间维度切换无效
**可能原因**：
- UI 事件绑定失败
- 状态管理错误
- 数据重新加载失败

**解决方案**：
1. 刷新页面重新加载
2. 检查 `DashboardViewState` 的状态
3. 验证事件处理函数的执行

## 维护建议

1. **定期备份**：重要修改前备份代码
2. **测试验证**：修改后验证各种场景
3. **文档更新**：功能变更时同步更新文档
4. **代码审查**：复杂修改建议多人审查
5. **版本控制**：使用版本标记管理代码变更

## 更新日志

### v2.2 (2025-07-10)
- ✅ 优化风险等级视图的颜色显示
- ✅ 新增正负数颜色格式化工具函数
- ✅ 总收益、日收益显示正数为绿色、负数为红色
- ✅ 当前APR、当前APR总比显示正数为绿色、负数为红色
- ✅ 总计行也应用相同的颜色规则

### v2.1 (2025-07-10)
- ✅ 更新文档中的行号引用，确保准确性
- ✅ 补充遗漏的函数说明和模块描述
- ✅ 在代码文件开头添加README阅读提醒
- ✅ 完善UI组件和表格渲染功能说明
- ✅ 修正开发者指引中的位置信息

### v2.0 (2025-07-09)
- ✅ 新增日期维度视图功能
- ✅ 实现时间范围严格过滤逻辑
- ✅ 优化 APR 计算基于 USDT 价值
- ✅ 完善错误处理和数据验证
- ✅ 模块化代码架构重构

### v1.0 (2025-06-01)
- ✅ 基础风险维度视图
- ✅ 项目数据统计和分析
- ✅ 基本的 APR 计算功能

## 快速参考

### 常用配置路径
```javascript
// 在 DashboardConfig 中修改
HISTORY_PATH: '"web3/farm/history"'           // 项目历史数据路径
RISK_LIMIT_PATH: 'web3/farm/data/risk_percentage'    // 风险配置文件
PRICES_FILE_PATH: 'web3/farm/data/token_prices'      // 价格数据文件
LEVEL_LIMIT_PATH: 'web3/farm/data/invest_level_limit' // 投资等级配置
```

### 关键计算公式速查
```javascript
// APR 计算
APR = ((lastBalance - firstBalance) / firstBalance) / days * 365 * 100

// 总收益计算
totalEarned = withdrawAmount - investAmount

// 日收益计算
dailyEarned = totalEarned / investDays

// 仓位百分比
positionPercent = projectAmount / totalAmount * 100
```

### 时间格式规范
- **项目文件日期**: `20250608 22:00:00` 或 `20250608`
- **价格数据日期**: `2025-06-08`
- **内部处理格式**: `yyyy-MM-dd`

### 状态字段说明
- **Status**: `Doing`(进行中) / `Done`(已完成)
- **Risk**: `0`(无风险) / `1`(低风险) / `2`(中风险) / `3`(高风险) / `4`(极高风险)
- **Level**: `1-5` 投资等级，数字越大仓位越重

## 示例场景

### 场景1：新增项目
1. 在 `history/doing/2025/` 下创建项目文件
2. 填写完整的 YAML 头部信息
3. 添加初始 balance 记录
4. 如需要，在 `token_prices.md` 中添加价格数据

### 场景2：项目追加投资
在项目文件中添加新记录：
```markdown
- [date:: 20250710 10:00:00] [balance:: 1500] [add:: 500] [remark:: "追加投资"]
```

### 场景3：项目完成
1. 修改项目文件的 `Status: Done`
2. 移动文件到 `history/done/2025/` 目录
3. 添加最终的 balance 记录

### 场景4：查看特定月份表现
1. 切换到日期维度视图
2. 选择月粒度
3. 查看对应月份的数据

## 📚 开发者快速参考表

### 常见修改需求对照表

| 需求 | 修改位置 | 影响范围 | 风险等级 |
|------|----------|----------|----------|
| **调整表格列顺序** | `buildRiskSummaryRow()` (957-1011行)<br>`buildProjectRow()` (1015-1043行)<br>`buildTimeSummaryRow()` (1299-1363行) | 表格显示 | 🟡 中等 |
| **修改APR计算** | `calculateAPR()` (484-508行)<br>`calculateUSDTBasedAPR()` (706-726行) | 所有APR显示 | 🔴 高 |
| **调整颜色主题** | `getRiskColoredText()` (154-167行)<br>`getAPRGapColoredText()` (170-183行) | 视觉效果 | 🟢 低 |
| **修改数据过滤** | 主执行逻辑 (1410行, 1409行) | 显示的项目 | 🟡 中等 |
| **调整时间范围** | `calculateProjectHistoryInTimeRange()` (530-603行) | 日期维度计算 | 🔴 高 |
| **修改表格样式** | `renderFixedTable()` (75-111行) | 表格外观 | 🟢 低 |
| **添加新列** | 各个 `buildXXXRow()` 函数 | 表格结构 | 🟡 中等 |
| **修改排序逻辑** | 主执行逻辑 (1439-1444行) | 数据排序 | 🟢 低 |

### 核心函数速查表

| 函数名 | 位置 | 功能 | 修改影响 |
|--------|------|------|----------|
| `parseRiskFile()` | 196-208行 | 解析风险配置 | 风险等级显示 |
| `parseLevelFile()` | 212-224行 | 解析投资等级 | 仓位状态计算 |
| `parsePriceData()` | 227-259行 | 解析价格数据 | USDT转换 |
| `calculateProjectHistory()` | 424-480行 | 项目历史计算 | 风险维度数据 |
| `calculateProjectHistoryInTimeRange()` | 530-603行 | 时间范围计算 | 日期维度数据 |
| `calculateAPR()` | 484-508行 | APR计算 | APR显示 |
| `calculateUSDTAmount()` | 607-693行 | USDT转换 | 金额显示 |
| `processProject()` | 813-867行 | 项目数据处理 | 项目信息 |
| `buildRiskSummaryRow()` | 957-1011行 | 风险汇总行 | 风险维度表格 |
| `buildProjectRow()` | 1015-1043行 | 项目详情行 | 风险维度表格 |
| `buildTimeSummaryRow()` | 1299-1363行 | 时间汇总行 | 日期维度表格 |
| `buildDateProjectRow()` | 1367-1396行 | 日期项目行 | 日期维度表格 |

### 配置文件修改指南

| 配置类型 | 文件路径 | 格式要求 | 修改影响 |
|----------|----------|----------|----------|
| **风险配置** | `web3/farm/data/risk_percentage.md` | `[risk:: 0] [cn:: 无风险] [percent:: 0.3] [expect_apr:: 0.05]` | 风险等级显示和计算 |
| **投资等级** | `web3/farm/data/invest_level_limit.md` | `[level:: 1] [cn:: 轻仓] [limit:: 1000]` | 仓位状态和建议 |
| **价格数据** | `web3/farm/data/token_prices.md` | `[token:: BTC] [date:: 2025-06-01] [price:: 70000]` | USDT价值转换 |
| **项目数据** | `web3/farm/history/doing/` | YAML头部 + balance记录 | 所有计算基础 |

### 样式定制速查

| 样式元素 | CSS变量/属性 | 修改位置 | 示例 |
|----------|--------------|----------|------|
| **表格边框** | `--background-modifier-border` | `renderFixedTable()` | `border: 1px solid #ddd` |
| **背景色** | `--background-primary` | `renderFixedTable()` | `background-color: #fff` |
| **字体大小** | `font-size` | `renderFixedTable()` | `font-size: 14px` |
| **风险颜色** | 硬编码颜色值 | `getRiskColoredText()` | `color: #28a745` |
| **APR颜色** | 硬编码颜色值 | `getAPRGapColoredText()` | `color: #dc3545` |
| **表格高度** | `max-height` | `renderFixedTable()` | `max-height: 80vh` |

### 错误排查检查清单

#### ✅ 数据不显示时检查
- [ ] 项目文件路径是否正确
- [ ] 项目文件YAML格式是否正确
- [ ] Status字段是否为"Doing"（风险维度）
- [ ] balance记录是否存在
- [ ] 日期格式是否正确

#### ✅ 计算错误时检查
- [ ] 价格数据是否完整
- [ ] 日期解析是否正常
- [ ] 数值字段是否为数字格式
- [ ] 时间范围设置是否正确

#### ✅ 样式问题时检查
- [ ] CSS变量是否正确引用
- [ ] HTML标签是否闭合
- [ ] 颜色值是否有效
- [ ] 表格结构是否完整

### 性能优化建议

| 场景 | 优化方法 | 实现位置 | 效果 |
|------|----------|----------|------|
| **大量项目** | 分页显示 | 主执行逻辑 | 减少渲染时间 |
| **频繁切换** | 数据缓存 | 全局缓存系统 | 提高响应速度 |
| **复杂计算** | 结果缓存 | 计算模块 | 避免重复计算 |
| **表格渲染** | 虚拟滚动 | 表格渲染器 | 优化大表格性能 |

## 联系方式

如有问题或建议，请联系：
- **技术支持**: 通过 Obsidian 社区或相关技术论坛
- **功能建议**: 提交 Issue 或直接修改代码
- **Bug 报告**: 详细描述复现步骤和错误信息

---

**最后更新**: 2025-07-10
**版本**: v2.2
**维护者**: Augment Agent
**文档状态**: ✅ 完整 | 🔄 持续更新

**重要提醒**: 在修改代码前请务必阅读本文档，特别是"开发者指引"和"高风险修改警告"部分。
